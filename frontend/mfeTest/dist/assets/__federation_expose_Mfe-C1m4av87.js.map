{"version": 3, "file": "__federation_expose_Mfe-C1m4av87.js", "sources": ["../../node_modules/react/cjs/react-jsx-runtime.production.js", "../../node_modules/react/jsx-runtime.js", "../../src/mfe/Mfe.tsx"], "sourcesContent": ["/**\n * @license React\n * react-jsx-runtime.production.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\nvar REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n  REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\");\nfunction jsxProd(type, config, maybeKey) {\n  var key = null;\n  void 0 !== maybeKey && (key = \"\" + maybeKey);\n  void 0 !== config.key && (key = \"\" + config.key);\n  if (\"key\" in config) {\n    maybeKey = {};\n    for (var propName in config)\n      \"key\" !== propName && (maybeKey[propName] = config[propName]);\n  } else maybeKey = config;\n  config = maybeKey.ref;\n  return {\n    $$typeof: REACT_ELEMENT_TYPE,\n    type: type,\n    key: key,\n    ref: void 0 !== config ? config : null,\n    props: maybeKey\n  };\n}\nexports.Fragment = REACT_FRAGMENT_TYPE;\nexports.jsx = jsxProd;\nexports.jsxs = jsxProd;\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-runtime.development.js');\n}\n", "import \"./Mfe.css\";\nimport { version } from \"../../package.json\";\n\nexport const Mfe = () => {\n\treturn (\n\t\t<div data-mfe-version={`${version}}`} className=\"mfe\">\n\t\t\tHello Micro Frontend Test\n\t\t</div>\n\t);\n};\n\nexport default Mfe;\n"], "names": ["jsxRuntimeModule", "require$$0", "<PERSON><PERSON>", "jsx", "version"], "mappings": ";;;;;;;;;;;AAWA,IAAI,qBAAqB,OAAO,IAAI,4BAA4B,GAC9D,sBAAsB,OAAO,IAAI,gBAAgB;AACnD,SAAS,QAAQ,MAAM,QAAQ,UAAU;AACvC,MAAI,MAAM;AACV,aAAW,aAAa,MAAM,KAAK;AACnC,aAAW,OAAO,QAAQ,MAAM,KAAK,OAAO;AAC5C,MAAI,SAAS,QAAQ;AACnB,eAAW,CAAA;AACX,aAAS,YAAY;AACnB,gBAAU,aAAa,SAAS,QAAQ,IAAI,OAAO,QAAQ;AAAA,EACjE;AAAS,eAAW;AAClB,WAAS,SAAS;AAClB,SAAO;AAAA,IACL,UAAU;AAAA,IACV;AAAA,IACA;AAAA,IACA,KAAK,WAAW,SAAS,SAAS;AAAA,IAClC,OAAO;AAAA,EACX;AACA;AACgB,2BAAA,WAAG;AACR,2BAAA,MAAG;AACd,2BAAA,OAAe;AC/B4B;AAClCA,aAAA,UAAUC;AACnB;;;ACDO,MAAMC,MAAMA,MAAM;AAEvB,SAAAC,kCAAA,IAAC,SAAI,oBAAkB,GAAGC,OAAO,KAAK,WAAU,OAAK,UAErD,4BAAA,CAAA;AAEF;", "x_google_ignoreList": [0, 1]}