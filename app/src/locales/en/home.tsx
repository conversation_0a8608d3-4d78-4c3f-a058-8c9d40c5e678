import type { Home } from "../types/home.types";

export const translation: Home = {
	languageName: "en",

	all: {
		yes: "Yes",
		no: "No",
		ok: "OK",
		cancel: "Cancel",
		areYouSure: "Are you sure ?",
		error: "An error has occurred, please try again later.",
	},

	splash: {
		title: "OS",
	},

	home: {
		title: "Applications",
		guest: "Guest",
	},

	user: {
		title: "User",
		email: "Email",
		password: "Password",
		login: "Login",
		logout: "Logout",
		welcome: "Welcome {{firstName}} {{lastName}}!",
		loading: "Loading user data...",
		error: "Login error",
	},

	calculator: {
		title: "Calculator",
	},

	camera: {
		title: "Camera",
	},

	clock: {
		title: "Clock",
	},

	weather: {
		title: "Weather",
		loading: "Loading weather data...",
		error: "Failed to load weather data",
		location: "Location",
		temperature: "Temperature",
		feelsLike: "Feels like",
		humidity: "Humidity",
		windSpeed: "Wind Speed",
		pressure: "Pressure",
		visibility: "Visibility",
		uvIndex: "UV Index",
		currentConditions: "Current Conditions",
		hourlyForecast: "Hourly Forecast",
		locationError: "Unable to get your location",
		locationPermissionDenied: "Location permission denied",
	},

	notes: {
		title: "Notes",
		notes: "Notes",
		save: "Save",
	},

	tetris: {
		title: "Tetris",
	},

	stocks: {
		title: "Stocks",
		exchange: "Exchange: {{exchange}}",
	},

	chat: {
		title: "Chat",
	},

	calendar: {
		title: "Calendar",
		today: "Today",
	},

	store: {
		title: "Store",
		saveApp: "Save application to desktop?",
		alreadySaved: "Application is already on dektop.",
	},

	spin: {
		title: "Spin",
		add: "Add Choice",
		ready: "Ready",
	},

	snake: {
		title: "Snake",
	},

	ninja: {
		title: "Ninja",
	},

	clashRoyale: {
		title: "Clash Royale",
		start: "START",
		back: "BACK",
		loading: "LOADING...",
	},

	speed: {
		title: "Speed",
	},

	chickenScream: {
		title: "Chicken Scream",
	},

	chwazi: {
		title: "Chwazi",
	},

	shush: {
		title: "Shush",
	},

	emojiFace: {
		title: "Emoji Face",
	},

	smileClock: {
		title: "Smile Clock",
	},

	board: {
		title: "Board",
	},

	mfe: {
		title: "Micro Frontend",
	},

	redis: {
		title: "Redis",
		menu: {
			data: {
				title: "Data",
				dataCenter: "Data Center",
				dataAccess: "Data Access",
			},
			settings: {
				title: "Settings",
				settings: "Settings",
				reports: "Reports",
				payments: "Payments",
			},
			about: {
				title: "About",
				support: "Support",
				about: "About",
			},
		},
		database: {
			title: "Database",
		},
		subscription: {
			title: "Subscription",
		},
		create: {
			title: "Create",
		},
	},

	featureFlag: {
		title: "Feature Flag",
	},

	test: {
		title: "Test",
	},

	testShared: {
		title: "Test Shared",
	},

	testTable: {
		title: "Test\nTable",
	},

	testTransition: {
		title: "Test\nTransition",
	},

	testAnimation: {
		title: "Test\nAnimation",
	},

	testCube: {
		title: "Test\nCube",
	},

	testRedis: {
		title: "Test\nRedis",
	},

	testTree: {
		title: "Test\nTree",
	},

	testMenu: {
		title: "Test\nMenu",
	},

	testCounter: {
		title: "Test\nCounter",
	},

	testAi: {
		title: "Test\nAI",
	},

	testEdit: {
		title: "Test\nEdit",
	},
};
