import type { Home } from "../types/home.types";

export const translation: Home = {
	languageName: "fi",

	all: {
		yes: "<PERSON><PERSON>",
		no: "<PERSON><PERSON>",
		ok: "OK",
		cancel: "<PERSON>uttaa",
		areYouSure: "<PERSON><PERSON><PERSON> varma ?",
		error: "Ta<PERSON>htui virhe, yrit<PERSON> myöhemmin uudelleen.",
	},

	splash: {
		title: "<PERSON>",
	},

	home: {
		title: "<PERSON><PERSON>",
		guest: "<PERSON><PERSON><PERSON>",
	},

	user: {
		title: "<PERSON><PERSON>yttäj<PERSON>",
		email: "<PERSON><PERSON>hköposti",
		password: "<PERSON><PERSON><PERSON>",
		login: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
		logout: "ulos",
		welcome: "Tervetu<PERSON>a {{firstName}} {{lastName}}!",
		loading: "Ladataan käyttäjätietoja...",
		error: "Kir<PERSON><PERSON>umisvirhe",
	},

	calculator: {
		title: "<PERSON><PERSON>",
	},

	camera: {
		title: "Ka<PERSON>a",
	},

	clock: {
		title: "Ke<PERSON>",
	},

	weather: {
		title: "<PERSON><PERSON><PERSON>",
		loading: "Ladataan säätietoja...",
		error: "Säätietojen lataus epäonnistui",
		location: "Sijainti",
		temperature: "<PERSON>ä<PERSON><PERSON><PERSON>a",
		feelsLike: "Tuntuu kuin",
		humidity: "Kosteus",
		windSpeed: "Tuulen nopeus",
		pressure: "Ilmanpaine",
		visibility: "Näkyvyys",
		uvIndex: "UV-indeksi",
		currentConditions: "Nykyiset olosuhteet",
		hourlyForecast: "Tuntikohtainen ennuste",
		locationError: "Sijaintia ei voida määrittää",
		locationPermissionDenied: "Sijaintilupa evätty",
	},

	notes: {
		title: "Huomautuksia",
		notes: "Huomautuksia",
		save: "Tallentaa",
	},

	stocks: {
		title: "Osakkeet",
		exchange: "Symboli: {{symbol}}",
	},

	chat: {
		title: "Puhua",
	},

	calendar: {
		title: "Kalenteri",
		today: "Tänään",
	},

	store: {
		title: "Kauppa",
		saveApp: "Tallenna sovellus työpöydälle?",
		alreadySaved: "Sovellus on jo työpöydällä.",
	},

	tetris: {
		title: "Tetris",
	},

	spin: {
		title: "Pyöritä",
		add: "Lisää Valinta",
		ready: "Valmis",
	},

	snake: {
		title: "Snake",
	},

	ninja: {
		title: "Ninja",
	},

	clashRoyale: {
		title: "Clash Royale",
		start: "ALKAA",
		back: "TAKAISIN",
		loading: "LADATAAN...",
	},

	speed: {
		title: "Nopeus",
	},

	chickenScream: {
		title: "Chicken Scream",
	},

	chwazi: {
		title: "Chwazi",
	},

	shush: {
		title: "Shush",
	},

	emojiFace: {
		title: "Emoji Face",
	},

	smileClock: {
		title: "Smile Clock",
	},

	board: {
		title: "Lauta",
	},

	mfe: {
		title: "Micro Frontend",
	},

	redis: {
		title: "Redis",
		menu: {
			data: {
				title: "Data",
				dataCenter: "Data Center",
				dataAccess: "Data Access",
			},
			settings: {
				title: "Settings",
				settings: "Settings",
				reports: "Reports",
				payments: "Payments",
			},
			about: {
				title: "About",
				support: "Support",
				about: "About",
			},
		},
		database: {
			title: "Database",
		},
		subscription: {
			title: "Subscription",
		},
		create: {
			title: "Create",
		},
	},

	featureFlag: {
		title: "Feature Flag",
	},

	test: {
		title: "Test",
	},

	testShared: {
		title: "Test Shared",
	},

	testTable: {
		title: "Test\nTable",
	},

	testTransition: {
		title: "Test\nTransition",
	},

	testAnimation: {
		title: "Test\nAnimation",
	},

	testCube: {
		title: "Test\nCube",
	},

	testRedis: {
		title: "Test\nRedis",
	},

	testTree: {
		title: "Test\nTree",
	},

	testMenu: {
		title: "Test\nMenu",
	},

	testCounter: {
		title: "Test\nCounter",
	},

	testAi: {
		title: "Test\nAI",
	},

	testEdit: {
		title: "Test\nEdit",
	},
};
