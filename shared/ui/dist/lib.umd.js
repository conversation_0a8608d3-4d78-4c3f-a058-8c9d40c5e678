(function(m,k){typeof exports=="object"&&typeof module<"u"?k(exports,require("react"),require("styled-components")):typeof define=="function"&&define.amd?define(["exports","react","styled-components"],k):(m=typeof globalThis<"u"?globalThis:m||self,k(m.ui={},m.React,m.styled))})(this,function(m,k,$){"use strict";var q={exports:{}},P={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var rr;function wr(){if(rr)return P;rr=1;var p=k,E=Symbol.for("react.element"),D=Symbol.for("react.fragment"),x=Object.prototype.hasOwnProperty,W=p.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Y={key:!0,ref:!0,__self:!0,__source:!0};function F(R,s,O){var v,g={},_=null,N=null;O!==void 0&&(_=""+O),s.key!==void 0&&(_=""+s.key),s.ref!==void 0&&(N=s.ref);for(v in s)x.call(s,v)&&!Y.hasOwnProperty(v)&&(g[v]=s[v]);if(R&&R.defaultProps)for(v in s=R.defaultProps,s)g[v]===void 0&&(g[v]=s[v]);return{$$typeof:E,type:R,key:_,ref:N,props:g,_owner:W.current}}return P.Fragment=D,P.jsx=F,P.jsxs=F,P}var j={};/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var er;function Cr(){return er||(er=1,process.env.NODE_ENV!=="production"&&function(){var p=k,E=Symbol.for("react.element"),D=Symbol.for("react.portal"),x=Symbol.for("react.fragment"),W=Symbol.for("react.strict_mode"),Y=Symbol.for("react.profiler"),F=Symbol.for("react.provider"),R=Symbol.for("react.context"),s=Symbol.for("react.forward_ref"),O=Symbol.for("react.suspense"),v=Symbol.for("react.suspense_list"),g=Symbol.for("react.memo"),_=Symbol.for("react.lazy"),N=Symbol.for("react.offscreen"),tr=Symbol.iterator,Dr="@@iterator";function Fr(r){if(r===null||typeof r!="object")return null;var e=tr&&r[tr]||r[Dr];return typeof e=="function"?e:null}var w=p.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function f(r){{for(var e=arguments.length,t=new Array(e>1?e-1:0),o=1;o<e;o++)t[o-1]=arguments[o];Ar("error",r,t)}}function Ar(r,e,t){{var o=w.ReactDebugCurrentFrame,i=o.getStackAddendum();i!==""&&(e+="%s",t=t.concat([i]));var l=t.map(function(a){return String(a)});l.unshift("Warning: "+e),Function.prototype.apply.call(console[r],console,l)}}var Ir=!1,$r=!1,Wr=!1,Yr=!1,Nr=!1,or;or=Symbol.for("react.module.reference");function Lr(r){return!!(typeof r=="string"||typeof r=="function"||r===x||r===Y||Nr||r===W||r===O||r===v||Yr||r===N||Ir||$r||Wr||typeof r=="object"&&r!==null&&(r.$$typeof===_||r.$$typeof===g||r.$$typeof===F||r.$$typeof===R||r.$$typeof===s||r.$$typeof===or||r.getModuleId!==void 0))}function Mr(r,e,t){var o=r.displayName;if(o)return o;var i=e.displayName||e.name||"";return i!==""?t+"("+i+")":t}function nr(r){return r.displayName||"Context"}function h(r){if(r==null)return null;if(typeof r.tag=="number"&&f("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."),typeof r=="function")return r.displayName||r.name||null;if(typeof r=="string")return r;switch(r){case x:return"Fragment";case D:return"Portal";case Y:return"Profiler";case W:return"StrictMode";case O:return"Suspense";case v:return"SuspenseList"}if(typeof r=="object")switch(r.$$typeof){case R:var e=r;return nr(e)+".Consumer";case F:var t=r;return nr(t._context)+".Provider";case s:return Mr(r,r.render,"ForwardRef");case g:var o=r.displayName||null;return o!==null?o:h(r.type)||"Memo";case _:{var i=r,l=i._payload,a=i._init;try{return h(a(l))}catch{return null}}}return null}var T=Object.assign,A=0,ar,ir,lr,ur,cr,sr,fr;function dr(){}dr.__reactDisabledLog=!0;function Vr(){{if(A===0){ar=console.log,ir=console.info,lr=console.warn,ur=console.error,cr=console.group,sr=console.groupCollapsed,fr=console.groupEnd;var r={configurable:!0,enumerable:!0,value:dr,writable:!0};Object.defineProperties(console,{info:r,log:r,warn:r,error:r,group:r,groupCollapsed:r,groupEnd:r})}A++}}function Ur(){{if(A--,A===0){var r={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:T({},r,{value:ar}),info:T({},r,{value:ir}),warn:T({},r,{value:lr}),error:T({},r,{value:ur}),group:T({},r,{value:cr}),groupCollapsed:T({},r,{value:sr}),groupEnd:T({},r,{value:fr})})}A<0&&f("disabledDepth fell below zero. This is a bug in React. Please file an issue.")}}var z=w.ReactCurrentDispatcher,J;function L(r,e,t){{if(J===void 0)try{throw Error()}catch(i){var o=i.stack.trim().match(/\n( *(at )?)/);J=o&&o[1]||""}return`
`+J+r}}var G=!1,M;{var Br=typeof WeakMap=="function"?WeakMap:Map;M=new Br}function vr(r,e){if(!r||G)return"";{var t=M.get(r);if(t!==void 0)return t}var o;G=!0;var i=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var l;l=z.current,z.current=null,Vr();try{if(e){var a=function(){throw Error()};if(Object.defineProperty(a.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(a,[])}catch(y){o=y}Reflect.construct(r,[],a)}else{try{a.call()}catch(y){o=y}r.call(a.prototype)}}else{try{throw Error()}catch(y){o=y}r()}}catch(y){if(y&&o&&typeof y.stack=="string"){for(var n=y.stack.split(`
`),d=o.stack.split(`
`),u=n.length-1,c=d.length-1;u>=1&&c>=0&&n[u]!==d[c];)c--;for(;u>=1&&c>=0;u--,c--)if(n[u]!==d[c]){if(u!==1||c!==1)do if(u--,c--,c<0||n[u]!==d[c]){var b=`
`+n[u].replace(" at new "," at ");return r.displayName&&b.includes("<anonymous>")&&(b=b.replace("<anonymous>",r.displayName)),typeof r=="function"&&M.set(r,b),b}while(u>=1&&c>=0);break}}}finally{G=!1,z.current=l,Ur(),Error.prepareStackTrace=i}var S=r?r.displayName||r.name:"",Or=S?L(S):"";return typeof r=="function"&&M.set(r,Or),Or}function qr(r,e,t){return vr(r,!1)}function zr(r){var e=r.prototype;return!!(e&&e.isReactComponent)}function V(r,e,t){if(r==null)return"";if(typeof r=="function")return vr(r,zr(r));if(typeof r=="string")return L(r);switch(r){case O:return L("Suspense");case v:return L("SuspenseList")}if(typeof r=="object")switch(r.$$typeof){case s:return qr(r.render);case g:return V(r.type,e,t);case _:{var o=r,i=o._payload,l=o._init;try{return V(l(i),e,t)}catch{}}}return""}var U=Object.prototype.hasOwnProperty,br={},pr=w.ReactDebugCurrentFrame;function B(r){if(r){var e=r._owner,t=V(r.type,r._source,e?e.type:null);pr.setExtraStackFrame(t)}else pr.setExtraStackFrame(null)}function Jr(r,e,t,o,i){{var l=Function.call.bind(U);for(var a in r)if(l(r,a)){var n=void 0;try{if(typeof r[a]!="function"){var d=Error((o||"React class")+": "+t+" type `"+a+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+typeof r[a]+"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.");throw d.name="Invariant Violation",d}n=r[a](e,a,o,t,null,"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED")}catch(u){n=u}n&&!(n instanceof Error)&&(B(i),f("%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).",o||"React class",t,a,typeof n),B(null)),n instanceof Error&&!(n.message in br)&&(br[n.message]=!0,B(i),f("Failed %s type: %s",t,n.message),B(null))}}}var Gr=Array.isArray;function K(r){return Gr(r)}function Kr(r){{var e=typeof Symbol=="function"&&Symbol.toStringTag,t=e&&r[Symbol.toStringTag]||r.constructor.name||"Object";return t}}function Hr(r){try{return gr(r),!1}catch{return!0}}function gr(r){return""+r}function hr(r){if(Hr(r))return f("The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.",Kr(r)),gr(r)}var I=w.ReactCurrentOwner,Xr={key:!0,ref:!0,__self:!0,__source:!0},yr,mr,H;H={};function Zr(r){if(U.call(r,"ref")){var e=Object.getOwnPropertyDescriptor(r,"ref").get;if(e&&e.isReactWarning)return!1}return r.ref!==void 0}function Qr(r){if(U.call(r,"key")){var e=Object.getOwnPropertyDescriptor(r,"key").get;if(e&&e.isReactWarning)return!1}return r.key!==void 0}function re(r,e){if(typeof r.ref=="string"&&I.current&&e&&I.current.stateNode!==e){var t=h(I.current.type);H[t]||(f('Component "%s" contains the string ref "%s". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref',h(I.current.type),r.ref),H[t]=!0)}}function ee(r,e){{var t=function(){yr||(yr=!0,f("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)",e))};t.isReactWarning=!0,Object.defineProperty(r,"key",{get:t,configurable:!0})}}function te(r,e){{var t=function(){mr||(mr=!0,f("%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)",e))};t.isReactWarning=!0,Object.defineProperty(r,"ref",{get:t,configurable:!0})}}var oe=function(r,e,t,o,i,l,a){var n={$$typeof:E,type:r,key:e,ref:t,props:a,_owner:l};return n._store={},Object.defineProperty(n._store,"validated",{configurable:!1,enumerable:!1,writable:!0,value:!1}),Object.defineProperty(n,"_self",{configurable:!1,enumerable:!1,writable:!1,value:o}),Object.defineProperty(n,"_source",{configurable:!1,enumerable:!1,writable:!1,value:i}),Object.freeze&&(Object.freeze(n.props),Object.freeze(n)),n};function ne(r,e,t,o,i){{var l,a={},n=null,d=null;t!==void 0&&(hr(t),n=""+t),Qr(e)&&(hr(e.key),n=""+e.key),Zr(e)&&(d=e.ref,re(e,i));for(l in e)U.call(e,l)&&!Xr.hasOwnProperty(l)&&(a[l]=e[l]);if(r&&r.defaultProps){var u=r.defaultProps;for(l in u)a[l]===void 0&&(a[l]=u[l])}if(n||d){var c=typeof r=="function"?r.displayName||r.name||"Unknown":r;n&&ee(a,c),d&&te(a,c)}return oe(r,n,d,i,o,I.current,a)}}var X=w.ReactCurrentOwner,Er=w.ReactDebugCurrentFrame;function C(r){if(r){var e=r._owner,t=V(r.type,r._source,e?e.type:null);Er.setExtraStackFrame(t)}else Er.setExtraStackFrame(null)}var Z;Z=!1;function Q(r){return typeof r=="object"&&r!==null&&r.$$typeof===E}function Rr(){{if(X.current){var r=h(X.current.type);if(r)return`

Check the render method of \``+r+"`."}return""}}function ae(r){{if(r!==void 0){var e=r.fileName.replace(/^.*[\\\/]/,""),t=r.lineNumber;return`

Check your code at `+e+":"+t+"."}return""}}var _r={};function ie(r){{var e=Rr();if(!e){var t=typeof r=="string"?r:r.displayName||r.name;t&&(e=`

Check the top-level render call using <`+t+">.")}return e}}function Tr(r,e){{if(!r._store||r._store.validated||r.key!=null)return;r._store.validated=!0;var t=ie(e);if(_r[t])return;_r[t]=!0;var o="";r&&r._owner&&r._owner!==X.current&&(o=" It was passed a child from "+h(r._owner.type)+"."),C(r),f('Each child in a list should have a unique "key" prop.%s%s See https://reactjs.org/link/warning-keys for more information.',t,o),C(null)}}function kr(r,e){{if(typeof r!="object")return;if(K(r))for(var t=0;t<r.length;t++){var o=r[t];Q(o)&&Tr(o,e)}else if(Q(r))r._store&&(r._store.validated=!0);else if(r){var i=Fr(r);if(typeof i=="function"&&i!==r.entries)for(var l=i.call(r),a;!(a=l.next()).done;)Q(a.value)&&Tr(a.value,e)}}}function le(r){{var e=r.type;if(e==null||typeof e=="string")return;var t;if(typeof e=="function")t=e.propTypes;else if(typeof e=="object"&&(e.$$typeof===s||e.$$typeof===g))t=e.propTypes;else return;if(t){var o=h(e);Jr(t,r.props,"prop",o,r)}else if(e.PropTypes!==void 0&&!Z){Z=!0;var i=h(e);f("Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?",i||"Unknown")}typeof e.getDefaultProps=="function"&&!e.getDefaultProps.isReactClassApproved&&f("getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.")}}function ue(r){{for(var e=Object.keys(r.props),t=0;t<e.length;t++){var o=e[t];if(o!=="children"&&o!=="key"){C(r),f("Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.",o),C(null);break}}r.ref!==null&&(C(r),f("Invalid attribute `ref` supplied to `React.Fragment`."),C(null))}}function xr(r,e,t,o,i,l){{var a=Lr(r);if(!a){var n="";(r===void 0||typeof r=="object"&&r!==null&&Object.keys(r).length===0)&&(n+=" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.");var d=ae(i);d?n+=d:n+=Rr();var u;r===null?u="null":K(r)?u="array":r!==void 0&&r.$$typeof===E?(u="<"+(h(r.type)||"Unknown")+" />",n=" Did you accidentally export a JSX literal instead of a component?"):u=typeof r,f("React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s",u,n)}var c=ne(r,e,t,i,l);if(c==null)return c;if(a){var b=e.children;if(b!==void 0)if(o)if(K(b)){for(var S=0;S<b.length;S++)kr(b[S],r);Object.freeze&&Object.freeze(b)}else f("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");else kr(b,r)}return r===x?ue(c):le(c),c}}function ce(r,e,t){return xr(r,e,t,!0)}function se(r,e,t){return xr(r,e,t,!1)}var fe=se,de=ce;j.Fragment=x,j.jsx=fe,j.jsxs=de}()),j}process.env.NODE_ENV==="production"?q.exports=wr():q.exports=Cr();var Sr=q.exports;const Pr=$.button.withConfig({displayName:"Button",componentId:"sc-1q8kq34-0"})(["display:flex;align-items:center;justify-content:center;outline:none;cursor:pointer;border:none;"," ","  ",""],p=>p.$variant==="full"&&$.css(["--label-color:hsl(0,0%,100%);--background-color:hsl(230,60%,50%);--border-color:hsl(230,60%,50%);color:var(--label-color);background-color:var(--background-color);border-color:var(--border-color);border-width:4px;border-style:solid;padding:10px 64px;border-radius:8px;font-size:18px;&:hover{--background-hover-color:hsl(230,60%,55%);background-color:var(--background-hover-color);}&:active{--background-active-color:hsl(230,60%,60%);background-color:var(--background-active-color);}&:disabled{--label-disabled-color:hsl(0,0%,100%);--background-disabled-color:hsl(0,0%,63%);--border-disabled-color:hsl(0,0%,63%);color:var(--label-disabled-color);background-color:var(--background-disabled-color);border-color:var(--border-disabled-color);}"]),p=>p.$variant==="stroke"&&$.css(["--label-color:hsl(230,60%,50%);--background-color:hsl(0,0%,100%);--border-color:hsl(230,60%,50%);color:var(--label-color);background-color:var(--background-color);border-color:var(--border-color);border-width:4px;border-style:solid;padding:10px 64px;border-radius:8px;font-size:18px;&:hover{--background-hover-color:hsl(230,55%,80%);background-color:var(--background-hover-color);}&:active{--background-active-color:hsl(230,55%,85%);background-color:var(--background-active-color);}&:disabled{--label-disabled-color:hsl(0,0%,63%);--background-disabled-color-color:hsl(0,0%,100%);--border-disabled-color:hsl(0,0%,63%);color:var(--label-disabled-color);background-color:var(--background-disabled-color);border-color:var(--border-disabled-color);}"]),p=>p.$variant==="link"&&$.css(["--label-color:hsl(230,60%,50%);--background-color:hsla(0,0%,0%,0);color:var(--label-color);background-color:var(--background-color);font-size:18px;&:hover{--label-color:hsl(230,55%,80%);color:var(--label-color);}&:active{--label-color:hsl(230,55%,85%);color:var(--label-color);}&:disabled{--label-color:hsl(0,0%,63%);color:var(--label-color);}"])),jr=({children:p,varian:E="full",...D})=>Sr.jsx(Pr,{...D,$variant:E,children:p});m.Button=jr,Object.defineProperty(m,Symbol.toStringTag,{value:"Module"})});
