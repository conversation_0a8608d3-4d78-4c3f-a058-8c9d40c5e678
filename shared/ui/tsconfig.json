{"compilerOptions": {"baseUrl": "./", "paths": {"@src/*": ["./src/*"], "@assets/*": ["./src/assets/*"], "@pages/*": ["./src/pages/*"], "@components/*": ["./src/components/*"]}, "target": "ESNext", "useDefineForClassFields": true, "lib": ["DOM", "DOM.Iterable", "ESNext"], "allowJs": false, "skipLibCheck": true, "esModuleInterop": false, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "ESNext", "moduleResolution": "Node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "sourceMap": true, "types": ["vitest/globals"], "noUnusedParameters": true, "noUnusedLocals": true}, "include": ["src", "**/*.ts", "**/*.d.ts", "**/*.tsx"], "references": [{"path": "./tsconfig.node.json"}]}